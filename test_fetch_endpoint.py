import pytest
import json
import os
from pathlib import Path
from fastapi.testclient import <PERSON><PERSON><PERSON>
from fetch_endpoint import app
from config import VALID_CLIENT_KEYS

# Create test client
client = TestClient(app)

# Test configuration
VALID_TEST_KEY = list(VALID_CLIENT_KEYS)[0] if VALID_CLIENT_KEYS else "550e8400-e29b-41d4-a716-446655440000"
INVALID_KEY = "invalid-key"
DATA_DIR = Path("../data")


def cleanup_test_files():
    """Clean up test files after tests."""
    files_to_remove = [
        DATA_DIR / "last_fetched.json",
        DATA_DIR / "data.json"
    ]
    for file_path in files_to_remove:
        if file_path.exists():
            file_path.unlink()


def test_fetch_endpoint_dry_run_valid_key():
    """Test fetch endpoint with valid key in dry run mode."""
    response = client.get(f"/fetch?key={VALID_TEST_KEY}&dryrun=1")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert data["dry_run"] is True
    assert "would_create" in data
    assert len(data["would_create"]) == 2
    assert "message" in data
    assert "timestamp" in data


def test_fetch_endpoint_actual_run_valid_key():
    """Test fetch endpoint with valid key in actual execution mode."""
    # Clean up before test
    cleanup_test_files()
    
    response = client.get(f"/fetch?key={VALID_TEST_KEY}&dryrun=0")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert data["dry_run"] is False
    assert "files_created" in data
    assert len(data["files_created"]) == 2
    assert "last_fetched_data" in data
    assert "data_file_preview" in data
    
    # Verify files were actually created
    assert (DATA_DIR / "last_fetched.json").exists()
    assert (DATA_DIR / "data.json").exists()
    
    # Verify file contents
    with open(DATA_DIR / "last_fetched.json", 'r') as f:
        last_fetched = json.load(f)
        assert "timestamp" in last_fetched
        assert "unix_timestamp" in last_fetched
    
    with open(DATA_DIR / "data.json", 'r') as f:
        data_content = json.load(f)
        assert data_content["status"] == "success"
        assert "data" in data_content
    
    # Clean up after test
    cleanup_test_files()


def test_fetch_endpoint_invalid_key():
    """Test fetch endpoint with invalid key."""
    response = client.get(f"/fetch?key={INVALID_KEY}&dryrun=1")
    
    assert response.status_code == 401
    data = response.json()
    assert "Invalid or unauthorized client key" in data["detail"]


def test_fetch_endpoint_missing_key():
    """Test fetch endpoint without key parameter."""
    response = client.get("/fetch?dryrun=1")
    
    assert response.status_code == 422  # Validation error


def test_fetch_endpoint_default_dryrun():
    """Test fetch endpoint with default dryrun value (should be 0)."""
    response = client.get(f"/fetch?key={VALID_TEST_KEY}")
    
    # This should work if the key is valid
    if VALID_CLIENT_KEYS:
        assert response.status_code == 200
        data = response.json()
        assert data["dry_run"] is False  # Default should be 0 (False)
    else:
        # If no valid keys configured, should fail with 401
        assert response.status_code == 401


def test_fetch_endpoint_invalid_uuid_format():
    """Test fetch endpoint with malformed UUID."""
    response = client.get("/fetch?key=not-a-uuid&dryrun=1")
    
    assert response.status_code == 401
    data = response.json()
    assert "Invalid or unauthorized client key" in data["detail"]


if __name__ == "__main__":
    # Run basic tests manually
    print("Running basic tests...")
    
    print("1. Testing dry run with valid key...")
    test_fetch_endpoint_dry_run_valid_key()
    print("✓ Dry run test passed")
    
    print("2. Testing invalid key...")
    test_fetch_endpoint_invalid_key()
    print("✓ Invalid key test passed")
    
    print("3. Testing malformed UUID...")
    test_fetch_endpoint_invalid_uuid_format()
    print("✓ Malformed UUID test passed")
    
    print("\nAll basic tests passed! Run 'pytest test_fetch_endpoint.py' for full test suite.")
